# Керівництво по використанню тегів у description

## Огляд

У проекті реалізована система контролю тегів у полі `description` в `db.json`. Тільки теги з конкретними id будуть відображатися на сайті, а теги без id або з невідповідними id будуть приховані.

## Дозволені типи тегів з id

### 1. Теги `<br>` з id
```html
<br id="br-1" />
<br id="br-2" />
<br id="br-3" />
```

### 2. Теги `<span>` з категоризованими id

#### Роки заснування:
```html
<span id="year-1">1956</span>
<span id="year-2">1985</span>
```

#### Назви студій:
```html
<span id="studio-1">Toei Animation</span>
<span id="studio-2">Kyoto Animation</span>
```

#### Виділені фрази:
```html
<span id="highlight-1">хіти</span>
<span id="highlight-2">випущені багато років тому</span>
```

## Приклад використання

### ✅ Правильно (буде відображено):
```json
{
  "description": "Заснована <span id=\"year-1\">1956</span> року. За більш ніж 50<br id=\"br-1\" />років існування, студія <span id=\"studio-1\">«Toei Animation»</span><br id=\"br-2\" />створила безліч популярних аніме."
}
```

### ❌ Неправильно (буде приховано):
```json
{
  "description": "Заснована <span>1956</span> року. За більш ніж 50<br />років існування, студія <span>«Toei Animation»</span><br />створила безліч популярних аніме."
}
```

## CSS правила

### Глобальні правила (globals.css):
```css
/* Тільки br теги з id будуть відображатися */
br[id] {
  display: block;
  margin: 4px 0;
  content: "";
}

/* Приховуємо br теги без id */
br:not([id]) {
  display: none !important;
}

/* Тільки span теги з відповідними id будуть відображатися */
span[id^="year-"],
span[id^="studio-"],
span[id^="highlight-"] {
  display: inline;
}

/* Приховуємо span теги без id або з невідповідними id */
span:not([id]),
span:not([id^="year-"]):not([id^="studio-"]):not([id^="highlight-"]) {
  display: none !important;
}
```

### Специфічні стилі для компонентів:
```css
/* Стилі для років */
.description span[id^="year-"] {
    font-weight: 600;
    color: #5a9bd4;
}

/* Стилі для назв студій */
.description span[id^="studio-"] {
    font-weight: 500;
    color: #4B7FCC;
    font-style: italic;
}

/* Стилі для виділених фраз */
.description span[id^="highlight-"] {
    background-color: rgba(75, 127, 204, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    color: #6bb6ff;
}
```

## Переваги системи

1. **Контроль якості**: Тільки затверджені теги з id будуть відображатися
2. **Безпека**: Запобігає випадковому додаванню небажаних тегів
3. **Консистентність**: Всі теги мають унікальні ідентифікатори
4. **Стилізація**: Можливість точного контролю стилів для різних типів контенту
5. **Легкість підтримки**: Легко знайти та змінити конкретні елементи

## Як додавати нові записи

1. Використовуйте тільки теги з відповідними id
2. Дотримуйтесь конвенції іменування:
   - `year-X` для років
   - `studio-X` для назв студій  
   - `highlight-X` для виділених фраз
   - `br-X` для переносів рядків
3. Переконайтеся, що id унікальні в межах всього файлу

## Тестування

Для перевірки роботи системи:
1. Додайте запис з тегами без id
2. Перевірте, що вони не відображаються на сайті
3. Додайте теги з правильними id
4. Переконайтеся, що вони відображаються з правильними стилями
