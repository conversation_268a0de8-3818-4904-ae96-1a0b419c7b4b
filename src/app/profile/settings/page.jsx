'use client';

import SettingsMenu from '@/components/SettingsMenu';
import styles from "./settings.module.css";
import {useState} from "react";

export default function ProfileSettingsPage () {
    const [activeTab, setActiveTab] = useState('basic_settings');

    const tabs = [
        { id: 'basic_settings', label: 'Основні налаштування' },
        { id: 'profile', label: 'Сповіщення' },
        { id: 'security', label: 'Безпека' },
        { id: 'list', label: 'Список аніме' },
        { id: 'payment', label: 'Платіжні дані' },
        { id: 'notifications', label: 'Сповіщення' },
        { id: 'customization', label: 'Кастомізація' }
    ];

    return (
        <div className={styles.container}>
            <img src="/Line 5.svg" className={styles.line5} />
            <img src="/Line 10.svg" className={styles.line10} />
            <img src="/Line 9.svg" className={styles.line9} />
            <header className={styles.header_settings}>
                <p style={{ color: '#fff', fontSize: '18px' }}>Налаштування</p>
            </header>
            <main>
                <SettingsMenu />
            </main>
            <img src="/Line 6.svg" className={styles.line6} />
            <img src="/Line 8.svg" className={styles.line8} />
            <img src="/Line 7.svg" className={styles.line7} />
        </div>
    );
}