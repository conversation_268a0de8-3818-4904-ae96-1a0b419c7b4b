'use client';

// import { useAuth } from "@/hooks/useAuth";
import styles from "./profile.module.css";

export default function ProfilePage () {
    // const { user, loading, logout } = useAuth();

    // if (loading) {
    //     return <div>Завантаження...</div>;
    // }
    //
    // if (!user) {
    //     return null;
    // }

    return (
        <div className={styles.container}>
            <h1>Профіль</h1>
            {/*<p>Ім'я: {user.name}</p>*/}
            {/*<p>Email: {user.email}</p>*/}
            {/*<button onClick={logout} className={styles.logoutButton}>*/}
            {/*    Вийти*/}
            {/*</button>*/}
        </div>
    );
}