'use client';

import { useEffect, useState } from "react";
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import styles from "./signup.module.css";

export default function SignupPage() {
    const router = useRouter();
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');

    useEffect(() => {
        const width = window.screen.width;
        const height = window.screen.height;

        fetch("/api/logScreenResolution", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({ width, height }),
        });
    }, []);

    return(
        <div className={styles.container}>
            <img src="/Line 5.svg" className={styles.line5} />
            <img src="/Line 10.svg" className={styles.line10} />
            <img src="/Line 9.svg" className={styles.line9} />
            <div className={styles.card}>
                <div>
                    <div className={styles.logo}>
                        <img src="/Group 1.svg" />
                    </div>
                    <div className={styles.login}>
                        <img src="/Line 4.svg" />
                        <h2 className={styles.title}>Реєстрація</h2>
                        <img src="/Line 3.svg" />
                    </div>
                    {error && <p className={styles.error}>{error}</p>}
                    <form>
                        <div className={styles.inputGroup}>
                            <div className={styles.inputContainer}>
                                <img src="/email.svg" className={styles.Icon} />
                                <input
                                    type="email"
                                    placeholder="E-mail"
                                    className={styles.customInput}
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    required
                                />
                            </div>
                        </div>
                        <div className={styles.inputGroup}>
                            <div className={styles.inputContainer}>
                                <img src="/password.svg" className={styles.Icon} />
                                <input
                                    type="password"
                                    placeholder="Пароль"
                                    className={styles.customInput}
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    required
                                />
                            </div>
                        </div>
                        <button type="submit" className={styles.button}>
                            Далі
                        </button>
                    </form>
                    <img src="/Line 3_2.svg" />
                </div>
                <p className={styles.socialText}>Реєстрація за допомогою</p>
                <div className={styles.socialIcons}>
                    <button
                        className={styles.icon}
                        onClick={() => router.push('/')}
                    >
                        <img className={styles.iconImage} src="/discord.svg" />
                    </button>
                    <button
                        className={styles.icon}
                        onClick={() => router.push('/')}
                    >
                        <img className={styles.iconImage} src="/google.svg" />
                    </button>
                    <button
                        className={styles.icon}
                        onClick={() => router.push('/')}
                    >
                        <img className={styles.iconImage} src="/telegram.svg" />
                    </button>
                </div>
            </div>
            <img src="/Line 6.svg" className={styles.line6} />
            <img src="/Line 8.svg" className={styles.line8} />
            <img src="/Line 7.svg" className={styles.line7} />
        </div>
    );
}

// "use client";
//
// import { useState } from "react";
// import { useRouter } from "next/navigation";
// import Link from "next/link";
// import styles from "./signup.module.css";
// import { registerUser } from "./../../services/authService";
//
// export default function SignupPage() {
//   const [name, setName] = useState("");
//   const [email, setEmail] = useState("");
//   const [password, setPassword] = useState("");
//   const [statusMsg, setStatusMsg] = useState("");
//   const [isLoading, setIsLoading] = useState(false);
//   const router = useRouter();
//
//   const handleSubmit = async (e) => {
//     e.preventDefault();
//     setIsLoading(true);
//     setStatusMsg("");
//
//     try {
//       const data = {
//         name: name || email.split("@")[0], // Use name if provided, otherwise use email prefix
//         email,
//         password,
//         password_confirmation: password,
//       };
//
//       console.log("Sending registration data:", data);
//
//       const response = await registerUser(data);
//       console.log("Registration successful:", response);
//       setStatusMsg("Registration successful!");
//
//       // Redirect after a brief delay to show success message
//       setTimeout(() => {
//         router.push("/signin");
//       }, 1500);
//     } catch (error) {
//       console.error("Registration error in component:", error);
//       setStatusMsg(`Registration failed: ${error.message}`);
//     } finally {
//       setIsLoading(false);
//     }
//   };
//
//   return (
//     <div className={styles.container}>
//       {/* Your existing UI elements */}
//       <div className={styles.card}>
//         <div>
//           <div className={styles.logo}>
//             <img src="/Group 1.svg" alt="Logo" />
//           </div>
//           <div className={styles.login}>
//             <img src="/Line 4.svg" alt="Line" />
//             <h2 className={styles.title}>Реєстрація</h2>
//             <img src="/Line 3.svg" alt="Line" />
//           </div>
//           <form onSubmit={handleSubmit}>
//             <div className={styles.inputGroup}>
//               <div className={styles.inputContainer}>
//                 <img
//                   src="/email.svg"
//                   className={styles.Icon}
//                   alt="Email icon"
//                 />
//                 <input
//                   type="email"
//                   placeholder="E-mail"
//                   className={styles.customInput}
//                   value={email}
//                   onChange={(e) => setEmail(e.target.value)}
//                   required
//                 />
//               </div>
//             </div>
//             <div className={styles.inputGroup}>
//               <div className={styles.inputContainer}>
//                 <img
//                   src="/password.svg"
//                   className={styles.Icon}
//                   alt="Password icon"
//                 />
//                 <input
//                   type="password"
//                   placeholder="Пароль"
//                   className={styles.customInput}
//                   value={password}
//                   onChange={(e) => setPassword(e.target.value)}
//                   required
//                 />
//               </div>
//             </div>
//             <button
//               className={styles.button}
//               type="submit"
//               disabled={isLoading}
//             >
//               {isLoading ? "Завантаження..." : "Далі"}
//             </button>
//           </form>
//           {statusMsg && <p className={styles.statusMsg}>{statusMsg}</p>}
//           <img src="/Line 3_2.svg" alt="Line" />
//           <div className={styles.login2}>
//             Якщо є аккаунт?{" "}
//             <Link href="/signin" className={styles.link}>
//               Увійти
//             </Link>
//           </div>
//         </div>
//         <p className={styles.socialText}>Реєстрація за допомогою</p>
//         <div className={styles.socialIcons}>{/* Your social buttons */}</div>
//       </div>
//       {/* Your remaining UI elements */}
//     </div>
//   );
// }
// >>>>>>> origin/main
// >>>>>>> 7b1c5c6de96ac6c0d5beb7c86584f8ac9ba0f296
