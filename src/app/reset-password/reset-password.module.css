.container {
    display: flex;
    position: relative;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: linear-gradient(180deg, rgba(73, 99, 138, 0.35) -15%, rgba(0, 0, 0, 0) 100%);
    background-color: #000;
    overflow: hidden;
}

.line5 {
    position: absolute;
    right: 1467px;
    bottom: 353px;
  }
  
  .line6 {
    position: absolute;
    left: 1777px;
    bottom: 155.69px;
  }
  
  .line7 {
    position: absolute;
    left: 1288px;
    top: 455px;
  }
  
  .line8 {
    position: absolute;
    left: 1604.87px;
  }
  
  .line9 {
    position: absolute;
    right: 1670px;
  }
  
  .line10 {
    position: absolute;
    right: 1185.52px;
    top: 657px;
  }

  .card {
    /*background: #000;*/
    padding: 55px;
    border-radius: 64px;
    /*box-shadow: 0 0 15px rgba(255, 0, 255, 0.5);*/
    text-align: center;
    width: 584px;
    height: 730px;
  }
