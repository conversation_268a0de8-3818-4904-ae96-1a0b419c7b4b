.container {
    display: flex;
    position: relative;
    flex-direction: column;
    padding-left: 84px;
    padding-right: 84px;
    min-height: 100vh;
    max-width: 100%;
    /*overflow: hidden;*/
    background: linear-gradient(180deg, rgba(73, 99, 138, 0.35) -15%, rgba(0, 0, 0, 0) 100%);
    background-color: #000;
    z-index: 0;
}

/* Декоративні лінії - точні позиції як на зображенні */
/* Загальні стилі для всіх декоративних ліній */
.line5, .line6, .line7, .line8, .line9, .line10 {
    pointer-events: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    cursor: default;
}
.line5 {
    position: fixed;
    right: 1467px;
    bottom: 353px;
    z-index: -1;
    opacity: 0.8;
}

.line6 {
    position: fixed;
    left: 1820px;
    bottom: 155.69px;
    z-index: -1;
    opacity: 0.8;
}

.line7 {
    position: fixed;
    left: 1335px;
    top: 455px;
    z-index: -1;
    opacity: 0.8;
}

.line8 {
    position: fixed;
    left: 1630px;
    z-index: -1;
    opacity: 0.8;
}

.line9 {
    position: fixed;
    right: 1670px;
    z-index: -1;
    opacity: 0.8;
}

.line10 {
    position: fixed;
    right: 1185.52px;
    top: 657px;
    z-index: -1;
    opacity: 0.8;
}

/* Приховуємо деякі декоративні елементи на малих екранах */
@media (max-width: 1600px) {
    .line6, .line8 {
        display: none;
    }
}

@media (max-width: 1400px) {
    .line5, .line7, .line9, .line10 {
        display: none;
    }
}

@media (max-width: 900px) {
    .line5, .line6, .line7, .line8, .line9, .line10 {
        display: none;
    }
}

.header_studies {
    display: flex;
    width: 100%;
    max-width: 100vw;
    padding-top: 24px;
    padding-bottom: 24px;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.01);
    backdrop-filter: blur(4.449999809265137px);
    z-index: 2;
    box-sizing: border-box;
}

.logo {
    width: 50px;
    height: 50px;
}

/* Секція "Студії" з випадаючими списками */
.studiesSection {
    position: relative;
    display: flex;
    /*gap: 24px;*/
    justify-content: space-between;
    align-items: center;
    margin-top: 32px;
    margin-bottom: 24px;
    width: 100%;
}

.studiesTitle {
    font-family: 'Inter', sans-serif;
    font-size: 32px;
    /*margin-bottom: 24px;*/
    font-weight: 700;
    color: #fff;
    margin: 0 0 24px 0;
}

.dropdownsContainer {
    display: flex;
    gap: 24px;
    align-items: center;
    justify-content: flex-end;
    /*margin-top: 24px;*/
    margin-bottom: 24px;
    width: 100%;
}

.dropdown {
    position: relative;
    display: inline-block;
}

.customSelect {
    appearance: none;
    background: rgba(0, 0, 0, 0.02);
    border: 1px solid #49638A;
    border-radius: 8px;
    padding: 12px 40px 12px 16px;
    color: #fff;
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    min-width: 150px;
    outline: none;
}

.customSelect:focus {
    border-color: #5a7bb5;
    box-shadow: 0 0 0 2px rgba(73, 99, 138, 0.2);
}

.customSelect option {
    background: #1a1a1a;
    color: #fff;
    padding: 8px;
}

.dropdownArrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #fff;
    font-size: 12px;
    pointer-events: none;
}

.studiesUnderline {
    position: absolute;
    bottom: 0px;
    left: 0;
    right: 0;
    width: 100%;
    opacity: 0.8;
    pointer-events: none;
    user-select: none;
}

.searchBar {
    display: flex;
    align-items: center;
    width: clamp(200px, 25vw, 326px);
    height: 55px;
    z-index: 2;
}

.notificationCenter {
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.header_buttons_studies {
    display: flex;
    gap: clamp(12px, 2vw, 24px);
    color: #fff;
    justify-content: flex-end;
    align-items: center;
    z-index: 2;
    flex-shrink: 0;
}

.header_profile_dropdown {
    display: flex;
    align-items: center;
    z-index: 2;
    flex-shrink: 0;
}

/* Адаптивність для Edge з вертикальними вкладками */
@media (max-width: 1200px) {
    .searchBar {
        width: clamp(150px, 20vw, 250px);
    }

    .header_buttons_studies {
        gap: 16px;
    }
}

/* Для вузьких екранів (Edge з вертикальними вкладками) */
@media (max-width: 900px) {
    .header_studies {
        flex-wrap: wrap;
        gap: 12px;
    }

    .searchBar {
        width: clamp(120px, 18vw, 200px);
    }

    .header_buttons_studies {
        gap: 12px;
    }

    .header_buttons_studies button {
        padding: 12px 20px !important;
        font-size: 14px !important;
    }

    .studiesSection {
        margin-top: 32px;
    }

    .studiesTitle {
        font-size: 28px;
    }

    .dropdownsContainer {
        justify-content: flex-end;
        gap: 16px;
        margin-top: 20px;
    }

    .customSelect {
        min-width: 120px;
        font-size: 13px;
        padding: 10px 32px 10px 12px;
    }
}

/* Для дуже вузьких екранів */
@media (max-width: 600px) {
    .header_studies {
        flex-direction: column;
        padding: 16px 8px;
        gap: 16px;
    }

    .searchBar {
        width: 100%;
        max-width: 300px;
    }

    .header_buttons_studies {
        width: 100%;
        justify-content: center;
    }

    .studiesSection {
        margin-top: 24px;
        margin-bottom: 20px;
    }

    .studiesTitle {
        font-size: 24px;
    }

    .dropdownsContainer {
        width: 100%;
        flex-direction: column;
        gap: 12px;
        justify-content: stretch;
        margin-top: 16px;
        margin-bottom: 24px;
    }

    .customSelect {
        width: 100%;
        min-width: auto;
        font-size: 14px;
    }
}
