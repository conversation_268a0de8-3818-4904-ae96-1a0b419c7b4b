.container {
    display: flex;
    position: relative;
    flex-direction: column;
    padding-left: 84px;
    padding-right: 84px;
    min-height: 100vh;
    max-width: 100%;
    background: linear-gradient(180deg, rgba(73, 99, 138, 0.35) -15%, rgba(0, 0, 0, 0) 100%);
    background-color: #000;
    z-index: 0;
}

/* Декоративні лінії */
.line5, .line6, .line7, .line8, .line9, .line10 {
    pointer-events: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    cursor: default;
}

.line5 {
    position: fixed;
    right: 1467px;
    bottom: 353px;
    z-index: -1;
    opacity: 0.8;
}

.line6 {
    position: fixed;
    left: 1820px;
    bottom: 155.69px;
    z-index: -1;
    opacity: 0.8;
}

.line7 {
    position: fixed;
    left: 1335px;
    top: 455px;
    z-index: -1;
    opacity: 0.8;
}

.line8 {
    position: fixed;
    left: 1630px;
    z-index: -1;
    opacity: 0.8;
}

.line9 {
    position: fixed;
    right: 1670px;
    z-index: -1;
    opacity: 0.8;
}

.line10 {
    position: fixed;
    right: 1185.52px;
    top: 657px;
    z-index: -1;
    opacity: 0.8;
}

/* Хедер */
.header {
    display: flex;
    width: 100%;
    padding-top: 24px;
    padding-bottom: 24px;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.01);
    backdrop-filter: blur(4.5px);
    z-index: 2;
}

.logo {
    width: 50px;
    height: 50px;
}

.studioTitle {
    font-family: 'Inter', sans-serif;
    font-size: 32px;
    font-weight: 700;
    color: #fff;
    margin: 0 0 0 0;
    width: 481px;
    height: 39px;
}

.studioContent {
    margin-top: 40px;
}

.studioInfo {
    display: flex;
    gap: 24px;
}

.studioImageContainer {
    max-width: 170px;
    max-height: 130px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
}

.studioImage {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.placeholderImage {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #49638A, #5a7bb5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.placeholderText {
    font-size: 120px;
    font-weight: bold;
    color: white;
    text-transform: uppercase;
}

.description {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    font-family: 'Inter', sans-serif;
    font-size: 18px;
    font-weight: 400;
}

/* Стилі тільки для br тегів з id */
.description br[id] {
    display: block;
    margin: 4px 0;
    content: "";
}

/* Приховуємо br теги без id */
.description br:not([id]) {
    display: none;
}

/* Специфічні стилі для різних типів span тегів з id */
.description span[id^="year-"] {
    font-weight: 600;
    color: #5a9bd4;
}

.description span[id^="studio-"] {
    font-weight: 500;
    color: #4B7FCC;
    font-style: italic;
}

.description span[id^="highlight-"] {
    background-color: rgba(75, 127, 204, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    color: #6bb6ff;
}

/* Приховуємо span теги без id або з невідповідними id */
.description span:not([id]) {
    display: none;
}

.description span:not([id^="year-"]):not([id^="studio-"]):not([id^="highlight-"]) {
    display: none;
}

/* Завантаження і помилки */
.loading, .error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #fff;
    height: 50vh;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(73, 99, 138, 0.3);
    border-top: 3px solid #49638A;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.backButton {
    margin-top: 16px;
    padding: 12px 24px;
    background: #49638A;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.backButton:hover {
    background: #5a7bb5;
}

/* Адаптивність */
@media (max-width: 1200px) {
    .studioInfo {
        gap: 30px;
    }
}

@media (max-width: 900px) {
    .container {
        padding-left: 40px;
        padding-right: 40px;
    }
    
    .studioInfo {
        flex-direction: column;
        gap: 24px;
    }
    
    .studioImageContainer {
        width: 200px;
        height: 200px;
        align-self: center;
    }
}

@media (max-width: 600px) {
    .container {
        padding-left: 20px;
        padding-right: 20px;
    }
    
    .studioTitle {
        font-size: 24px;
    }
    
    .studioMeta {
        grid-template-columns: 1fr;
    }
    
    .studioImageContainer {
        width: 150px;
        height: 150px;
    }
    
    .placeholderText {
        font-size: 80px;
    }
}
