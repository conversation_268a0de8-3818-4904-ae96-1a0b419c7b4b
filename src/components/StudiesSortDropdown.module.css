.dropdown {
    position: relative;
    display: inline-block;
}

.dropdownButton {
    appearance: none;
    background: rgba(0, 0, 0, 0.02);
    border: 1px solid #49638A;
    border-radius: 8px;
    padding: 12px 40px 12px 16px;
    color: #fff;
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    min-width: 150px;
    outline: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    text-align: left;
}

.dropdownButton:hover {
    border-color: #5a7bb5;
    background: rgba(73, 99, 138, 0.1);
}

.dropdownButton:focus {
    border-color: #5a7bb5;
    box-shadow: 0 0 0 2px rgba(73, 99, 138, 0.2);
}

.arrow {
    color: #fff;
    font-size: 12px;
    transition: transform 0.3s ease;
    margin-left: 8px;
}

.arrowUp {
    transform: rotate(180deg);
}

.dropdownMenu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(20, 20, 20, 0.95);
    border: 1px solid #49638A;
    border-radius: 8px;
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    list-style: none;
    padding: 0;
    margin: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    max-height: 250px;
    overflow-y: auto;
}

.dropdownMenu li {
    margin: 0;
    padding: 0;
}

.dropdownItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 12px 16px;
    background: none;
    border: none;
    color: #fff;
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    text-align: left;
    transition: background-color 0.2s ease;
}

.dropdownItem:hover {
    background: rgba(73, 99, 138, 0.2);
}

.dropdownItem.active {
    background: rgba(73, 99, 138, 0.3);
    font-weight: 500;
}

.checkmark {
    color: #5a7bb5;
    font-weight: bold;
    font-size: 12px;
}

/* Адаптивність */
@media (max-width: 900px) {
    .dropdownButton {
        min-width: 120px;
        font-size: 13px;
        padding: 10px 32px 10px 12px;
    }
    
    .dropdownItem {
        padding: 10px 12px;
        font-size: 13px;
    }
}

@media (max-width: 600px) {
    .dropdownButton {
        width: 100%;
        min-width: auto;
        font-size: 14px;
    }
    
    .dropdownMenu {
        max-height: 200px;
    }
}

/* Анімація появи меню */
.dropdownMenu {
    opacity: 0;
    transform: translateY(-10px);
    animation: dropdownAppear 0.2s ease forwards;
}

@keyframes dropdownAppear {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Кастомний скролбар */
.dropdownMenu::-webkit-scrollbar {
    width: 6px;
}

.dropdownMenu::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.dropdownMenu::-webkit-scrollbar-thumb {
    background: rgba(73, 99, 138, 0.5);
    border-radius: 3px;
}

.dropdownMenu::-webkit-scrollbar-thumb:hover {
    background: rgba(73, 99, 138, 0.7);
}
