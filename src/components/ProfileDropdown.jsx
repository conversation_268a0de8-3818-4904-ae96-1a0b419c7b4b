import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import styles from './ProfileDropdown.module.css';

const ProfileDropdown = ({ user, logout }) => {
    const [isOpen, setIsOpen] = useState(false);

    return (
        <div
            className={styles.dropdown}
            onMouseEnter={() => setIsOpen(true)}
            onMouseLeave={() => setIsOpen(false)}
        >
            <Image
                src={user.profile_image || '/default-profile.png'}
                alt="Profile"
                width={40}
                height={40}
                className={styles.profileImage}
            />
            {isOpen && (
                <ul className={styles.menu}>
                    <li>
                        <Link href="/profile" className={styles.menuItem}>
                            Профіль
                        </Link>
                    </li>
                    <li>
                        <Link href="/profile/settings" className={styles.menuItem}>
                            Налаштування
                        </Link>
                    </li>
                    <li>
                        <button onClick={logout} className={styles.menuItem}>
                            Вихід
                        </button>
                    </li>
                </ul>
            )}
        </div>
    );
};

export default ProfileDropdown;