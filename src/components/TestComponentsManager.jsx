'use client';

import { useState } from 'react';
import AuthTestButton from './AuthTestButton';
import ResponsiveTestPanel from './ResponsiveTestPanel';
import styles from './TestComponents.module.css';

const TestComponentsManager = () => {
    const [showAuth, setShowAuth] = useState(true);
    const [showResponsive, setShowResponsive] = useState(true);
    const [isManagerVisible, setIsManagerVisible] = useState(false);

    if (!isManagerVisible) {
        return (
            <button
                onClick={() => setIsManagerVisible(true)}
                style={{
                    position: 'fixed',
                    top: '120px',
                    left: '104px', // 84px padding + 20px відступ
                    background: '#49638A',
                    color: 'white',
                    border: 'none',
                    padding: '8px 12px',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    fontSize: '12px',
                    zIndex: 1002
                }}
            >
                🔧 Test Controls
            </button>
        );
    }

    return (
        <>
            {/* Manager Panel */}
            <div style={{
                position: 'fixed',
                top: '160px',
                left: '104px', // 84px padding + 20px відступ
                background: 'rgba(0, 0, 0, 0.9)',
                color: 'white',
                padding: '12px',
                borderRadius: '8px',
                fontSize: '12px',
                zIndex: 1002,
                border: '1px solid #49638A',
                minWidth: '150px'
            }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                    <strong>🔧 Test Controls</strong>
                    <button
                        onClick={() => setIsManagerVisible(false)}
                        style={{
                            background: 'transparent',
                            color: 'white',
                            border: 'none',
                            cursor: 'pointer',
                            fontSize: '14px'
                        }}
                    >
                        ✕
                    </button>
                </div>
                
                <div style={{ display: 'flex', flexDirection: 'column', gap: '6px' }}>
                    <label style={{ display: 'flex', alignItems: 'center', gap: '6px', cursor: 'pointer' }}>
                        <input
                            type="checkbox"
                            checked={showAuth}
                            onChange={(e) => setShowAuth(e.target.checked)}
                        />
                        Auth Test Button
                    </label>
                    
                    <label style={{ display: 'flex', alignItems: 'center', gap: '6px', cursor: 'pointer' }}>
                        <input
                            type="checkbox"
                            checked={showResponsive}
                            onChange={(e) => setShowResponsive(e.target.checked)}
                        />
                        Responsive Panel
                    </label>
                </div>
            </div>

            {/* Test Components */}
            {showAuth && <AuthTestButton />}
            {showResponsive && <ResponsiveTestPanel />}
        </>
    );
};

export default TestComponentsManager;
