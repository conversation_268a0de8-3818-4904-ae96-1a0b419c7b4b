.toggleSwitch {
    display: inline-block;
    cursor: pointer;
    outline: none;
    transition: all 0.3s ease;
}

.toggleSwitch:focus {
    box-shadow: 0 0 0 2px rgba(73, 99, 138, 0.3);
    border-radius: 20px;
}

.slider {
    position: relative;
    display: inline-block;
    border-radius: 20px;
    transition: all 0.3s ease;
    border: 1px solid #49638A;
}

.thumb {
    position: absolute;
    top: 2px;
    border-radius: 50%;
    background: #fff;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Розміри */
.small .slider {
    width: 32px;
    height: 18px;
}

.small .thumb {
    width: 14px;
    height: 14px;
    left: 2px;
}

.medium .slider {
    width: 44px;
    height: 24px;
}

.medium .thumb {
    width: 20px;
    height: 20px;
    left: 2px;
}

.large .slider {
    width: 56px;
    height: 30px;
}

.large .thumb {
    width: 26px;
    height: 26px;
    left: 2px;
}

/* Стани */
.off {
    background: rgba(0, 0, 0, 0.1);
}

.on {
    background: #49638A;
}

/* Позиція thumb при включенні */
.small .on .thumb {
    transform: translateX(14px);
}

.medium .on .thumb {
    transform: translateX(18px);
}

.large .on .thumb {
    transform: translateX(24px);
}

/* Disabled стан */
.disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.disabled .slider {
    background: rgba(0, 0, 0, 0.05);
}

.disabled .thumb {
    background: #ccc;
}

/* Hover ефекти */
.toggleSwitch:not(.disabled):hover .slider {
    border-color: #5a7bb5;
}

.toggleSwitch:not(.disabled):hover .on {
    background: #5a7bb5;
}

/* Анімація при зміні стану */
.slider {
    animation: none;
}

.toggleSwitch:active:not(.disabled) .slider {
    transform: scale(0.95);
}

/* Адаптивність */
@media (max-width: 768px) {
    .large .slider {
        width: 44px;
        height: 24px;
    }
    
    .large .thumb {
        width: 20px;
        height: 20px;
    }
    
    .large .on .thumb {
        transform: translateX(18px);
    }
}
