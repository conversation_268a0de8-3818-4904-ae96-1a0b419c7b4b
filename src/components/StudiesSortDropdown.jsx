'use client';

import { useState } from 'react';
import styles from './StudiesSortDropdown.module.css';

const StudiesSortDropdown = ({ onSortChange, currentSort }) => {
    const [isOpen, setIsOpen] = useState(false);

    const sortOptions = [
        { value: 'name_asc', label: 'А-Я' },
        { value: 'name_desc', label: 'Я-А' },
        { value: 'popularity_desc', label: 'За популярністю' },
        { value: 'rating_desc', label: 'За рейтингом' },
        { value: 'year_desc', label: 'За роком (новіші)' },
        { value: 'year_asc', label: 'За роком (старіші)' }
    ];

    const currentOption = sortOptions.find(option => option.value === currentSort) || sortOptions[0];

    const handleOptionClick = (option) => {
        onSortChange(option.value);
        setIsOpen(false);
    };

    return (
        <div className={styles.dropdown}>
            <button 
                className={styles.dropdownButton}
                onClick={() => setIsOpen(!isOpen)}
                aria-expanded={isOpen}
            >
                {currentOption.label}
                <span className={`${styles.arrow} ${isOpen ? styles.arrowUp : ''}`}>▼</span>
            </button>
            
            {isOpen && (
                <ul className={styles.dropdownMenu}>
                    {sortOptions.map((option) => (
                        <li key={option.value}>
                            <button
                                className={`${styles.dropdownItem} ${
                                    option.value === currentSort ? styles.active : ''
                                }`}
                                onClick={() => handleOptionClick(option)}
                            >
                                {option.label}
                                {option.value === currentSort && (
                                    <span className={styles.checkmark}>✓</span>
                                )}
                            </button>
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
};

export default StudiesSortDropdown;
