'use client';

import { useState } from 'react';
import styles from './ToggleSwitch.module.css';

const ToggleSwitch = ({ 
    initialState = false, 
    onToggle = () => {}, 
    disabled = false,
    size = 'medium' // 'small', 'medium', 'large'
}) => {
    const [isOn, setIsOn] = useState(initialState);

    const handleToggle = () => {
        if (disabled) return;
        
        const newState = !isOn;
        setIsOn(newState);
        onToggle(newState);
    };

    return (
        <div 
            className={`${styles.toggleSwitch} ${styles[size]} ${disabled ? styles.disabled : ''}`}
            onClick={handleToggle}
            role="switch"
            aria-checked={isOn}
            tabIndex={disabled ? -1 : 0}
            onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleToggle();
                }
            }}
        >
            <div className={`${styles.slider} ${isOn ? styles.on : styles.off}`}>
                <div className={styles.thumb} />
            </div>
        </div>
    );
};

export default ToggleSwitch;
