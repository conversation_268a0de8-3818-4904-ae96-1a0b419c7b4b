'use client';

import { useState } from 'react';
import styles from './StudiesSortDropdown.module.css'; // Використовуємо ті ж стилі

const StudiesFilterDropdown = ({ onFilterChange, currentFilter }) => {
    const [isOpen, setIsOpen] = useState(false);

    const filterOptions = [
        { value: 'all', label: 'Всі студії' },
        { value: 'few_releases', label: 'Мало релізів (1-10)' },
        { value: 'medium_releases', label: 'Середньо релізів (11-50)' },
        { value: 'many_releases', label: 'Багато релізів (50+)' },
        { value: 'active', label: 'Активні студії' },
        { value: 'popular', label: 'Популярні студії' }
    ];

    const currentOption = filterOptions.find(option => option.value === currentFilter) || filterOptions[0];

    const handleOptionClick = (option) => {
        onFilterChange(option.value);
        setIsOpen(false);
    };

    return (
        <div className={styles.dropdown}>
            <button 
                className={styles.dropdownButton}
                onClick={() => setIsOpen(!isOpen)}
                aria-expanded={isOpen}
            >
                {currentOption.label}
                <span className={`${styles.arrow} ${isOpen ? styles.arrowUp : ''}`}>▼</span>
            </button>
            
            {isOpen && (
                <ul className={styles.dropdownMenu}>
                    {filterOptions.map((option) => (
                        <li key={option.value}>
                            <button
                                className={`${styles.dropdownItem} ${
                                    option.value === currentFilter ? styles.active : ''
                                }`}
                                onClick={() => handleOptionClick(option)}
                            >
                                {option.label}
                                {option.value === currentFilter && (
                                    <span className={styles.checkmark}>✓</span>
                                )}
                            </button>
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
};

export default StudiesFilterDropdown;
