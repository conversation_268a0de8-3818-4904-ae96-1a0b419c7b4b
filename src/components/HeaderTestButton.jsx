'use client';

const HeaderTestButton = ({ showTestComponents, toggleTestComponents }) => {
    return (
        <button
            onClick={toggleTestComponents}
            style={{
                background: showTestComponents ? 'rgba(73, 99, 138, 0.7)' : 'rgba(73, 99, 138, 0.3)',
                color: 'white',
                border: '1px solid #49638A',
                borderRadius: '6px',
                padding: '8px 12px',
                cursor: 'pointer',
                fontSize: '12px',
                fontFamily: 'Inter, sans-serif',
                fontWeight: '500',
                transition: 'all 0.3s ease',
                whiteSpace: 'nowrap'
            }}
            onMouseEnter={(e) => {
                e.target.style.background = showTestComponents ? 'rgba(73, 99, 138, 0.8)' : 'rgba(73, 99, 138, 0.5)';
            }}
            onMouseLeave={(e) => {
                e.target.style.background = showTestComponents ? 'rgba(73, 99, 138, 0.7)' : 'rgba(73, 99, 138, 0.3)';
            }}
        >
            🔧 Test {showTestComponents ? '✓' : ''}
        </button>
    );
};

export default HeaderTestButton;
