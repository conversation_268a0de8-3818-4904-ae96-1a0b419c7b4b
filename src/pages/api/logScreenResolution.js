<<<<<<< HEAD
export default function handler(req, res) {
  if (req.method === "POST") {
    const { width, height } = req.body;
    console.log(`Screen resolution: ${width}x${height}`);
    res.status(200).json({ message: "Resolution logged" });
  } else {
    res.status(405).json({ message: "Only POST requests allowed" });
  }
}
=======
<<<<<<< HEAD
export default function handler(req, res) {
    if (req.method === "POST") {
      const { width, height } = req.body;
      console.log(`Screen resolution: ${width}x${height}`);
      res.status(200).json({ message: "Resolution logged" });
    } else {
      res.status(405).json({ message: "Only POST requests allowed" });
    }
=======
export default function handler(req, res) {
    if (req.method === "POST") {
      const { width, height } = req.body;
      console.log(`Screen resolution: ${width}x${height}`);
      res.status(200).json({ message: "Resolution logged" });
    } else {
      res.status(405).json({ message: "Only POST requests allowed" });
    }
>>>>>>> origin/main
  }
>>>>>>> 7b1c5c6de96ac6c0d5beb7c86584f8ac9ba0f296
